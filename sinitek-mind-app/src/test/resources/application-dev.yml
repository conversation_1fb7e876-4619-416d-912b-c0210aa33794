sinicube:
  tempdir:
    # dev使用各操作系统通用的临时目录
    path: ${java.io.tmpdir}
  interceptors:
    frontend-api:
      excludePatterns:
        - /mind/open-api/**
        - /mind/**
spring:
  ai:
    ## milvus向量数据库
    vectorstore:
      milvus:
        client:
          host: "**************"
          port: 47377
    openai:
      base-url: https://api.deepseek.com
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
    mcp:
      client:
        enabled: false
        name: "sinitek-mind-mcp-client"
        version: 1.0.0
        request-timeout: 30s
        type: async
        sse:
          connections:
            sinitek-mind-server:
              url: "http://abc-url"

  elasticsearch:
    rest:
      uris:
        - http://*************:9200
      username: elastic
      password: 9r65Ty
  data:
    mongodb:
      uri: ***************************************************************************************************
  datasource:
    username: root
    password: sinitek
    url: ************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    # 连接池的配置信息
    druid:
      # 初始化大小，最小，最大
      initialSize: 10
      minIdle: 1
      maxActive: 600
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,log4j
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: sinitek-druid
        allow:
      web-stat-filter:
        enabled: true
    # PGSQL向量数据库配置
#    vector:
#      username: username
#      password: password
#      url: **********************************************
#      driver-class-name: org.postgresql.Driver
#      type: com.alibaba.druid.pool.DruidDataSource
#      # Druid连接池配置
#      druid:
#        initialSize: 5
#        minIdle: 5
#        maxActive: 20
#        maxWait: 60000
#        timeBetweenEvictionRunsMillis: 60000
#        minEvictableIdleTimeMillis: 300000
#        validationQuery: SELECT 1
#        testWhileIdle: true
#        testOnBorrow: false
#        testOnReturn: false
#        poolPreparedStatements: true
#        maxPoolPreparedStatementPerConnectionSize: 20
#        filters: stat