package com.sinitek.mind.model.controller;

import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.dto.*;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.ResultActions;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * ModelController - 单元测试
 *
 * <AUTHOR>
 * date 2025-07-29
 */
@Slf4j
public class ModelControllerTest extends MindBaseTestApplication {

    private static final String LIST_TEST_MODEL_ID = "Qwen3-32B-AWQ";
    private static final String CRUD_TEST_MODEL_ID = "gpt-4.1";

    @Test
    @SneakyThrows
    @DisplayName("测试模型CRUD接口")
    public void testModelCrud() {
        // 新增模型
        String modelId = saveModel();

        // 更新模型
        updateModel(modelId);

        // 查看模型详情
        getModelDetail(modelId);

        // 删除模型
        deleteModel(modelId);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试模型列表查询接口")
    public void testModelList() {
        // 数据准备：先按唯一标识删除已有测试数据
        cleanListTestModel();

        // 创建列表查询专用测试数据
        createListTestModel();

        // 执行列表查询测试
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/ai/model/list")
        );

        // 验证查询结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data[*].model", hasItem(LIST_TEST_MODEL_ID)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/list接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试更新默认模型接口")
    public void testUpdateDefault() {
        // 设置请求参数
        ModelUpdateDefaultRequest updateRequest = new ModelUpdateDefaultRequest();
        updateRequest.setLlm("Qwen3-32B-AWQ");
        updateRequest.setEmbedding("bge-m3");
        updateRequest.setDatasetTextLLM("deepseek-r1:latest");
        updateRequest.setDatasetImageLLM("glm-4v-flash");

        // 执行更新操作
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/updateDefault")
                        .content(JsonUtil.toJsonString(updateRequest))
        );

        // 验证更新结果
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/updateDefault接口的返回结果: {}", contentAsString);

        // 获取更新后的配置并验证
        ResultActions configPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/common/system/getInitData")
        );

        String configContent = configPerform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.data.defaultModels.llm.model", is("Qwen3-32B-AWQ")))
                .andReturn().getResponse().getContentAsString();

        log.info("/system/getInitData接口的返回结果: {}", configContent);
    }

    @SneakyThrows
    private String saveModel() {
        SystemModelDTO modelDTO = createCrudTestModel();
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/update")
                        .content(JsonUtil.toJsonString(modelDTO))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/update接口的新增返回结果: {}", contentAsString);
        return CRUD_TEST_MODEL_ID;
    }

    @SneakyThrows
    private void updateModel(String modelId) {
        ModelUpdateRequest updateRequest = new ModelUpdateRequest();
        updateRequest.setModel(modelId);
        // 修改isActive状态来测试更新功能
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("isActive", true);
        updateRequest.setMetadata(metadata);

        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/update")
                        .content(JsonUtil.toJsonString(updateRequest))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/update接口的更新返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void getModelDetail(String modelId) {
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/ai/model/detail")
                        .param("model", modelId)
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)))
                .andExpect(jsonPath("$.data.model", is(modelId)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/detail接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void deleteModel(String modelId) {
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/delete")
                        .param("model", modelId)
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/delete接口的返回结果: {}", contentAsString);
    }

    @SneakyThrows
    private void cleanListTestModel() {
        try {
            ModelDeleteDTO modelDeleteDTO = new ModelDeleteDTO();
            modelDeleteDTO.setModel(LIST_TEST_MODEL_ID);
            mockMvc.perform(
                    postMockRequestForAdmin("/mind/api/core/ai/model/delete")
                            .content(JsonUtil.toJsonString(modelDeleteDTO))
            );
        } catch (Exception e) {
            log.info("清理列表测试模型数据时发生异常，可能模型不存在: {}", e.getMessage());
        }
    }

    @SneakyThrows
    private void createListTestModel() {
        ModelUpdateRequest modelUpdateRequest = createListTestModelDTO();
        mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/update")
                        .content(JsonUtil.toJsonString(modelUpdateRequest))
        ).andExpect(status().isOk());
    }

    private ModelUpdateRequest createListTestModelDTO() {
        ModelUpdateRequest modelUpdateRequest = new ModelUpdateRequest();
        modelUpdateRequest.setModel(LIST_TEST_MODEL_ID);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", LIST_TEST_MODEL_ID);
        metadata.put("provider", ModelProviderConstant.OTHER);
        metadata.put("type", "llm");
        metadata.put("isActive", true);
        metadata.put("isCustom", true);
        metadata.put("isDefault", true);
        metadata.put("requestUrl", "http://192.168.22.246:9997/");
        metadata.put("maxContext", 100000);
        metadata.put("quoteMaxToken", 100000);
        modelUpdateRequest.setMetadata(metadata);
        return modelUpdateRequest;
    }

    private SystemModelDTO createCrudTestModel() {
        SystemModelDTO dto = new SystemModelDTO();
        dto.setProvider("OpenAI");
        dto.setModel(CRUD_TEST_MODEL_ID);
        dto.setName("gpt-4.1");
        dto.setType("llm");
        dto.setIsActive(true);
        dto.setIsCustom(true);
        dto.setMaxContext(1000000);
        dto.setMaxResponse(32000);
        dto.setQuoteMaxToken(1000000);
        dto.setMaxTemperature(1.2);
        dto.setShowTopP(true);
        dto.setVision(true);
        dto.setDatasetProcess(true);
        dto.setUsedInClassify(true);
        dto.setUsedInExtractFields(true);
        dto.setUsedInToolCall(true);
        dto.setFunctionCall(true);
        dto.setToolChoice(true);
        return dto;
    }



    @Test
    @SneakyThrows
    @DisplayName("测试更新配置文件接口")
    public void testUpdateWithJson() {
        // 先获取当前配置
        ResultActions getPerform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/ai/model/getConfigJson")
        );
        String configJson = getPerform.andReturn().getResponse().getContentAsString();
        Map<String, Object> responseMap = JsonUtil.toJavaObject(configJson, Map.class);
        List<SystemModelDTO> configList = JsonUtil.toJavaObjectList(JsonUtil.toJsonString(responseMap.get("data")), SystemModelDTO.class);

        // 修改第一个配置项的名称
        if (!configList.isEmpty()) {
            SystemModelDTO firstModel = configList.get(0);
            firstModel.setName(firstModel.getName() + "-test");
        }

        // 执行更新操作
        ModelUpdateWithJsonDTO updateDTO = new ModelUpdateWithJsonDTO();
        updateDTO.setConfig(JsonUtil.toJsonString(configList));

        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/ai/model/updateWithJson")
                        .content(JsonUtil.toJsonString(updateDTO))
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/updateWithJson接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @DisplayName("测试模型测试接口")
    public void testTestModel() {
        // 使用列表测试模型进行测试
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/ai/model/test")
                        .param("model", LIST_TEST_MODEL_ID)
        );

        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)))
                .andReturn().getResponse().getContentAsString();

        log.info("/model/test接口的返回结果: {}", contentAsString);
    }
}