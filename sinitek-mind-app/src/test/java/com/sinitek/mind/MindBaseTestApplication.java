package com.sinitek.mind;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.SirmApplication;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultMatcher;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Mind测试基层类
 *
 * <AUTHOR>
 * date 2025/07/22
 */
@Slf4j
@Rollback
//@Transactional
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = SirmApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class MindBaseTestApplication {

    @Autowired
    public MockMvc mockMvc;

    @Autowired
    public ObjectMapper objectMapper;

    public static final String ACCESS_TOKEN_NAME = "accesstoken";

    public String adminToken = "temp";

    /**
     * get方式Mock Request
     *
     * @param url
     * @return
     */
    public MockHttpServletRequestBuilder getMockRequestForAdmin(String url) {
        return MockMvcRequestBuilders
                .get(url)
                .header(ACCESS_TOKEN_NAME, adminToken);
    }

    /**
     * post方式Mock Request
     *
     * @param url
     * @return
     */
    public MockHttpServletRequestBuilder postMockRequestForAdmin(String url) {
        return MockMvcRequestBuilders
                .post(url)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding(StandardCharsets.UTF_8.name())
                .header(ACCESS_TOKEN_NAME, adminToken);
    }

    /**
     * mockMvc 通用预期判断
     *
     * @param request
     * @throws Exception
     */
    public MvcResult mockMvcCommonExpect(MockHttpServletRequestBuilder request) throws Exception {
        return mockMvc.perform(request)
                .andExpect(status().isOk())
                .andExpect(getResultCodeMatcher())
                .andReturn();
    }

    public void printLog(Object t) {
        log.info(JsonUtil.toJsonString(t));
    }

    /**
     * 返回 resultcode断言 SUCCESS_CODE的 ResultMatcher
     *
     * @return
     */
    public ResultMatcher getResultCodeMatcher() {
        return jsonPath("$.resultcode", is(RequestResult.SUCCESS_CODE));
    }

    /**
     * 返回 resultcode断言 messageCode的 ResultMatcher
     * 一般用于测异常
     * @return
     */
    public ResultMatcher getPredictCodeMatcher(String code) {
        return jsonPath("$.resultcode", is(code));
    }
}
