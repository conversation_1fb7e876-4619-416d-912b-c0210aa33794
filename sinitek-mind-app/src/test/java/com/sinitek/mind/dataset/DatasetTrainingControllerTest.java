package com.sinitek.mind.dataset;

import com.jayway.jsonpath.JsonPath;
import com.sinitek.mind.dataset.core.DatasetTrainingVectorTask;
import com.sinitek.mind.dataset.dto.DatasetDataDTO;
import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.mind.dataset.dto.TrainingErrorQueryDTO;
import com.sinitek.mind.dataset.dto.TrainingRequestDTO;
import com.sinitek.mind.dataset.enumerate.DatasetTrainingStatusEnum;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.dataset.service.IDatasetTrainingService;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.web.servlet.ResultActions;

import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * DatasetTrainingController-单元测试
 *
 * <AUTHOR>
 * date 2025-07-26
 */
@Slf4j
public class DatasetTrainingControllerTest extends CommonDatasetTest {

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private DatasetTrainingVectorTask datasetTrainingVectorTask;

    @Autowired
    private IDatasetTrainingService datasetTrainingService;

    @Autowired
    private IDatasetDataService datasetDataService;

    @Test
    @SneakyThrows
    @DisplayName("测试训练失败数据的全套流程")
    public void testQueryUpdateAndDeleteFailedTrainings() {
        // 创建知识库
        String datasetId = this.createDataset("单元测试之无法索引知识库", "bge-base-zh");

        // 上传测试文件
        String fileId = uploadDatasetFile(new ClassPathResource("data/dataset/collection/携宁SiniCube开放开发平台软件V1.3（软件著作权申请表）.docx").getFile());

        // 创建数据集集合
        String collectionId = createCollectionByFileId(datasetId, fileId, "单元测试之无法索引集合");

        // 触发向量训练任务
        triggerVectorTrainingTask();

        // 测试获取数据集训练队列状态接口
        String queueResp = getTrainingQueueStatus(datasetId);
        log.info("获取训练队列状态返回结果: {}", queueResp);

        // 查询失败的训练数据
        List<DatasetTrainingDTO> datasetTrainingList = queryFailedTrainings(collectionId);

        // 更新训练数据状态为待处理（重试接口）
        String testQ = "测试问题内容";
        updateTrainingStatusToPending(datasetId, collectionId, datasetTrainingList.get(0).getDataId(), testQ);

        // 查询单个训练数据详情
        String detailResp = getTrainingDataDetail(datasetId, collectionId, datasetTrainingList.get(0).getDataId());
        log.info("查询训练数据详情返回结果: {}", detailResp);
        
        // 验证训练数据text字段是否更新
        String text = JsonPath.read(detailResp, "$.data.text");
        assertThat(text).isEqualTo(testQ);
        
        // 查询对应的DatasetData验证q字段更新
        DatasetDataDTO dataDetail = datasetDataService.detail(datasetTrainingList.get(0).getDataId());
        assertThat(dataDetail.getQ()).isEqualTo(testQ);

        // 删除失败的训练数据
        deleteFailedTrainings(datasetId, collectionId, datasetTrainingList);

        // 验证删除结果
        verifyDeletionResult(collectionId);
    }

    /**
     * 触发向量训练任务
     */
    private void triggerVectorTrainingTask() {
        datasetTrainingVectorTask.processPendingTrainings();
    }

    /**
     * 获取训练队列状态
     * @param datasetId 知识库ID
     * @return 训练队列状态的JSON响应字符串
     * @throws Exception 请求过程中可能抛出的异常
     */
    private String getTrainingQueueStatus(String datasetId) throws Exception {
        ResultActions queueAction = mockMvc.perform(
            getMockRequestForAdmin("/mind/api/core/dataset/training/getDatasetTrainingQueue")
                .param("datasetId", datasetId)
        );
        return queueAction.andExpect(status().isOk())
            .andExpect(jsonPath("$.data.rebuildingCount", greaterThanOrEqualTo(0)))
            .andExpect(jsonPath("$.data.trainingCount", greaterThan(0)))
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * 查询失败的训练数据
     * @param collectionId 集合ID
     * @return 失败训练数据列表
     * @throws Exception 查询过程中可能抛出的异常
     */
    private List<DatasetTrainingDTO> queryFailedTrainings(String collectionId) throws Exception {
        TrainingErrorQueryDTO queryDTO = new TrainingErrorQueryDTO();
        queryDTO.setCollectionId(collectionId);

        ResultActions getErrorAction = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/training/getTrainingError")
                        .content(JsonUtil.toJsonString(queryDTO))
        );
        String errorResp = getErrorAction.andExpect(status().isOk())
                .andExpect(jsonPath("$.data.total", greaterThan(0)))
                .andExpect(jsonPath("$.data.list[0].errorMsg", notNullValue()))
                .andReturn().getResponse().getContentAsString();
        log.info("查询失败训练数据返回结果: {}", errorResp);

        List<Object> jsonList = JsonPath.read(errorResp, "$.data.list");
        List<DatasetTrainingDTO> datasetTrainingList = objectMapper.convertValue(jsonList,
                objectMapper.getTypeFactory().constructCollectionType(List.class, DatasetTrainingDTO.class));

        assertThat(datasetTrainingList).isNotNull();
        assertThat(datasetTrainingList.size()).isGreaterThan(0);
        return datasetTrainingList;
    }

    /**
     * 更新训练数据状态为待处理（重试接口）
     * @param datasetId 知识库ID
     * @param collectionId 集合ID
     * @param dataId 数据ID
     * @throws Exception 更新过程中可能抛出的异常
     */
    private void updateTrainingStatusToPending(String datasetId, String collectionId, String dataId, String q) throws Exception {
        TrainingRequestDTO updateDTO = new TrainingRequestDTO();
        updateDTO.setDatasetId(datasetId);
        updateDTO.setCollectionId(collectionId);
        updateDTO.setDataId(dataId);
        updateDTO.setQ(q);

        ResultActions updateAction = mockMvc.perform(
            postMockRequestForAdmin("/mind/api/core/dataset/training/updateTrainingData")
                .content(JsonUtil.toJsonString(updateDTO))
        );
        String updateResp = updateAction.andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();
        log.info("更新训练数据状态返回结果: {}", updateResp);
    }

    /**
     * 查询单个训练数据详情
     * @param datasetId 知识库ID
     * @param collectionId 集合ID
     * @param dataId 数据ID
     * @return 训练数据详情的JSON响应字符串
     * @throws Exception 查询过程中可能抛出的异常
     */
    private String getTrainingDataDetail(String datasetId, String collectionId, String dataId) throws Exception {
        TrainingRequestDTO detailDTO = new TrainingRequestDTO();
        detailDTO.setDatasetId(datasetId);
        detailDTO.setCollectionId(collectionId);
        detailDTO.setDataId(dataId);

        ResultActions getDetailAction = mockMvc.perform(
            postMockRequestForAdmin("/mind/api/core/dataset/training/getTrainingDataDetail")
                .content(JsonUtil.toJsonString(detailDTO))
        );
        return getDetailAction.andExpect(status().isOk())
            .andExpect(jsonPath("$.data.status", is(DatasetTrainingStatusEnum.PENDING.getValue())))
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * 删除失败的训练数据
     * @param datasetId 知识库ID
     * @param collectionId 集合ID
     * @param trainingList 训练数据列表
     * @throws Exception 删除过程中可能抛出的异常
     */
    private void deleteFailedTrainings(String datasetId, String collectionId, List<DatasetTrainingDTO> trainingList) throws Exception {
        for (DatasetTrainingDTO datasetTrainingDTO : trainingList) {
            TrainingRequestDTO deleteDTO = new TrainingRequestDTO();
            deleteDTO.setDatasetId(datasetId);
            deleteDTO.setCollectionId(collectionId);
            deleteDTO.setDataId(datasetTrainingDTO.getDataId());

            ResultActions deleteAction = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/training/deleteTrainingData")
                    .content(JsonUtil.toJsonString(deleteDTO))
            );
            String deleteContentString = deleteAction.andExpect(status().isOk())
                    .andReturn().getResponse().getContentAsString();
            log.info("删除失败训练数据结果: {}", deleteContentString);
        }
    }

    /**
     * 验证删除结果
     * @param collectionId 集合ID
     * @throws Exception 验证过程中可能抛出的异常
     */
    private void verifyDeletionResult(String collectionId) throws Exception {
        TrainingErrorQueryDTO queryDTO = new TrainingErrorQueryDTO();
        queryDTO.setCollectionId(collectionId);

        ResultActions verifyAction = mockMvc.perform(
            postMockRequestForAdmin("/mind/api/core/dataset/training/getTrainingError")
                .content(JsonUtil.toJsonString(queryDTO))
        );
        String verifyResp = verifyAction.andReturn().getResponse().getContentAsString();
        Integer remainingTotal = JsonPath.read(verifyResp, "$.data.total");
        assertThat(remainingTotal).isEqualTo(0);
    }
}