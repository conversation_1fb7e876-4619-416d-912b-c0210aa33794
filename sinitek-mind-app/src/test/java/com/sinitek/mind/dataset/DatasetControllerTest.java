package com.sinitek.mind.dataset;

import com.sinitek.mind.dataset.constant.DatasetTestConstant;
import com.sinitek.mind.dataset.dto.DatasetListRequest;
import com.sinitek.mind.dataset.dto.DatasetUpdateRequest;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.ResultActions;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * DatasetController-单元测试
 *
 * <AUTHOR>
 * date 2025-07-22
 */
@Slf4j
public class DatasetControllerTest extends CommonDatasetTest {

    @Autowired
    private IDatasetService datasetService;

    @Test
    @SneakyThrows
    @Order(1)
    @DisplayName("测试创建知识库接口")
    public void testCreateDataset() {
        this.createDataset(DatasetTestConstant.DATASET_UNIT_NAME, "bge-base-zh");
    }

    @Test
    @SneakyThrows
    @Order(2)
    @DisplayName("测试更新知识库接口")
    public void testUpdateDataset() {
        String datasetId = datasetService.getDatasetIdByName(DatasetTestConstant.DATASET_UNIT_NAME);
        DatasetUpdateRequest updateRequest = new DatasetUpdateRequest();
        updateRequest.setId(datasetId);
        updateRequest.setName("单元测试知识库-更新");
        updateRequest.setIntro("单元测试更新知识库");
        updateRequest.setAvatar("");
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/update")
                        .content(JsonUtil.toJsonString(updateRequest))
        );
        String contentAsString = perform.andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        log.info("/dataset/update接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @Order(3)
    @DisplayName("测试获取知识库详情接口")
    public void testGetDatasetDetail() {
        String datasetId = datasetService.getDatasetIdByName(DatasetTestConstant.DATASET_UNIT_NAME);
        ResultActions perform = mockMvc.perform(
                getMockRequestForAdmin("/mind/api/core/dataset/detail")
                        .param("id", datasetId)
        );
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.data.name", containsString(DatasetTestConstant.DATASET_UNIT_NAME)))
                .andExpect(jsonPath("$.data.tmbId", notNullValue()))
                .andReturn().getResponse().getContentAsString();
        log.info("/dataset/detail接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @Order(4)
    @DisplayName("测试获取知识库列表接口")
    public void testGetDatasetList() {
        DatasetListRequest listRequest = new DatasetListRequest();
        listRequest.setSearchKey("单元测试知识库");
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/list")
                        .content(JsonUtil.toJsonString(listRequest))
        );
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();
        log.info("/dataset/list接口的返回结果: {}", contentAsString);
    }

    @Test
    @SneakyThrows
    @Order(5)
    @DisplayName("测试删除知识库接口")
    public void testDeleteDataset() {
        String datasetId = datasetService.getDatasetIdByName(DatasetTestConstant.DATASET_UNIT_NAME);
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/delete")
                        .param("id", datasetId)
        );
        String contentAsString = perform.andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        log.info("/dataset/delete接口的返回结果: {}", contentAsString);
    }
} 