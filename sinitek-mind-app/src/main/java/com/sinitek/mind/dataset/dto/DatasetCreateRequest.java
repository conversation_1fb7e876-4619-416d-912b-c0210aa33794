package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.enumerate.DatasetCollectionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库创建请求DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 */
@Data
public class DatasetCreateRequest {

    @Schema(description = "父级ID")
    private String parentId;

    @Schema(description = "知识库ID (可选，作为parentId备选)")
    private String datasetId;

    @Schema(description = "知识库名称", required = true)
    private String name;

    /**
     * @see DatasetCollectionTypeEnum
     */
    @Schema(description = "知识库类型")
    private String type;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "介绍")
    private String intro;

    @Schema(description = "向量模型")
    private String vectorModel;

    @Schema(description = "代理模型")
    private String agentModel;

    @Schema(description = "视觉语言模型")
    private String vlmModel;
} 