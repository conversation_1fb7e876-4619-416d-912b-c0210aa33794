package com.sinitek.mind.model.service;

import com.sinitek.mind.model.entity.ModelInvokeLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;

/**
 * 模型调用日志服务接口
 *
 * <AUTHOR>
 * date 2025-07-08
 */
public interface IModelInvokeLogService {

    /**
     * 保存模型调用日志
     * @param log 日志对象
     * @return 保存后的日志对象
     */
    ModelInvokeLog saveLog(ModelInvokeLog log);

    /**
     * 创建新的调用日志
     * @param modelName 模型名称
     * @param endpoint 接口地址
     * @param userId 用户ID
     * @return 创建的日志对象
     */
    ModelInvokeLog createLog(String modelName, String endpoint, String userId);

    /**
     * 更新日志响应信息
     * @param requestId 请求ID
     * @param status 状态码
     * @param inputTokens 输入token数
     * @param outputTokens 输出token数
     * @return 更新后的日志对象
     */
    ModelInvokeLog updateLogResponse(String requestId, Integer status, Integer inputTokens, Integer outputTokens);

    /**
     * 根据请求ID查询日志
     * @param requestId 请求ID
     * @return 日志对象
     */
    ModelInvokeLog findByRequestId(String requestId);

    /**
     * 分页查询日志
     * @param modelName 模型名称
     * @param status 状态码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<ModelInvokeLog> queryLogs(String modelName, String status, Date startTime, Date endTime, Pageable pageable);

    /**
     * 删除指定日期之前的日志
     * @param date 截止日期
     * @return 删除的记录数
     */
    long deleteLogsBefore(Date date);
} 