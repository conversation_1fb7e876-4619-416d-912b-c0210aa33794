package com.sinitek.mind.model.service.impl;

import com.sinitek.mind.model.entity.ModelInvokeLog;
import com.sinitek.mind.model.repository.ModelInvokeLogRepository;
import com.sinitek.mind.model.service.IModelInvokeLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 模型调用日志服务实现
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Service
public class ModelInvokeLogServiceImpl implements IModelInvokeLogService {

    @Autowired
    private ModelInvokeLogRepository modelInvokeLogRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public ModelInvokeLog saveLog(ModelInvokeLog log) {
        if (StringUtils.isBlank(log.getRequestId())) {
            log.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        }
        
        return modelInvokeLogRepository.save(log);
    }

    @Override
    public ModelInvokeLog createLog(String modelName, String endpoint, String userId) {
        ModelInvokeLog log = new ModelInvokeLog();
        log.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        log.setModelName(modelName);
        log.setEndpoint(endpoint);
        log.setUserId(userId);
        log.setRequestTime(new Date());
        
        return modelInvokeLogRepository.save(log);
    }

    @Override
    public ModelInvokeLog updateLogResponse(String requestId, Integer status, Integer inputTokens, Integer outputTokens) {
        ModelInvokeLog log = modelInvokeLogRepository.findByRequestId(requestId);
        
        if (log != null) {
            log.setStatus(status);
            log.setInputTokens(inputTokens);
            log.setOutputTokens(outputTokens);
            log.setResponseTime(new Date());
            
            return modelInvokeLogRepository.save(log);
        }
        
        return null;
    }

    @Override
    public ModelInvokeLog findByRequestId(String requestId) {
        return modelInvokeLogRepository.findByRequestId(requestId);
    }

    @Override
    public Page<ModelInvokeLog> queryLogs(String modelName, String status, Date startTime, Date endTime, Pageable pageable) {
        Query query = new Query();

        if (startTime != null) {
            query.addCriteria(Criteria.where("requestTime").gte(startTime));
        }

        if (endTime != null) {
            query.addCriteria(Criteria.where("responseTime").lte(endTime));
        }

        if (StringUtils.isNotBlank(modelName)) {
            query.addCriteria(Criteria.where("modelName").is(modelName));
        }

        if (status != null && !"all".equals(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }

        long count = mongoTemplate.count(query, ModelInvokeLog.class);
        List<ModelInvokeLog> logs = mongoTemplate.find(query.with(pageable), ModelInvokeLog.class);

        return new PageImpl<>(logs, pageable, count);
    }

    @Override
    public long deleteLogsBefore(Date date) {
        Query query = new Query();
        query.addCriteria(Criteria.where("requestTime").lt(date));
        return mongoTemplate.remove(query, ModelInvokeLog.class).getDeletedCount();
    }
} 