package com.sinitek.mind.model.controller;

import com.sinitek.mind.model.dto.ModelInvokeLogDTO;
import com.sinitek.mind.model.dto.ModelInvokeLogUsageDTO;
import com.sinitek.mind.model.entity.ModelInvokeLog;
import com.sinitek.mind.model.service.IModelInvokeLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模型调用日志Controller
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@RestController
@RequestMapping("/mind/aiproxy/logs")
@Tag(name = "模型调用日志", description = "模型调用日志相关接口")
public class ModelInvokeLogController {

    @Autowired
    private IModelInvokeLogService modelInvokeLogService;

    @GetMapping("/search")
    @Operation(summary = "分页查询模型调用日志")
    public ResponseEntity<Map<String, Object>> queryLogs(
            @Parameter(description = "模型名称") @RequestParam(required = false) String modelName,
            @Parameter(description = "状态码") @RequestParam(value = "code_type", required = false) String status,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @Parameter(description = "页码") @RequestParam(value = "p", defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(value = "per_page", defaultValue = "10") int size) {
        
        Page<ModelInvokeLog> logPage = modelInvokeLogService.queryLogs(
                modelName, status, startTime, endTime, PageRequest.of(page, size));

        long total = logPage.getTotalElements();
        List<ModelInvokeLogDTO> dtoList = logPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("total", total);
        dataMap.put("logs", dtoList);

        Map<String, Object> result = new HashMap<>();
        result.put("data", dataMap);
        result.put("success", true);
        
        return ResponseEntity.ok(result);
    }
    /**
     * 将实体对象转换为DTO对象
     * @param log 实体对象
     * @return DTO对象
     */
    private ModelInvokeLogDTO convertToDTO(ModelInvokeLog log) {
        ModelInvokeLogDTO dto = new ModelInvokeLogDTO();
        BeanUtils.copyProperties(log, dto);
        dto.setModel(log.getModelName());

        // 请求id
        dto.setRequest_id(log.getRequestId());

        // 计算耗时(最终单位秒)
        Date requestTime = log.getRequestTime();
        Date responseTime = log.getResponseTime();
        dto.setCreated_at(responseTime);
        dto.setRequest_at(requestTime);

        // Tokens使用情况
        Integer inputTokens = log.getInputTokens();
        Integer outputTokens = log.getOutputTokens();

        ModelInvokeLogUsageDTO usage = new ModelInvokeLogUsageDTO();
        usage.setInput_tokens(inputTokens);
        usage.setOutput_tokens(outputTokens);
        if (ObjectUtils.isNotEmpty(inputTokens) && ObjectUtils.isNotEmpty(outputTokens)) {
            usage.setTotal_tokens(inputTokens + outputTokens);
        }
        dto.setUsage(usage);

        // 异常信息
        String errorMessage = log.getErrorMessage();
        dto.setContent(errorMessage);
        return dto;
    }
} 