package com.sinitek.mind.common.support;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 统一API响应DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：所有接口统一返回结构
 */
@Data
@Schema(description = "统一API响应")
public class ApiResponse<T> {

    @Schema(description = "状态码")
    private int code;

    @Schema(description = "状态文本")
    private String statusText;

    @Schema(description = "消息")
    private String message;

    @Schema(description = "数据")
    private T data;

    public ApiResponse() {}

    public ApiResponse(int code, String statusText, String message, T data) {
        this.code = code;
        this.statusText = statusText;
        this.message = message;
        this.data = data;
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "", "", data);
    }
    
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200, "", "", null);
    }

    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, "error", message, null);
    }
    
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(500, "error", message, null);
    }
} 