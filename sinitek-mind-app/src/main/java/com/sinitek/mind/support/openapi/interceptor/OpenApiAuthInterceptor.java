package com.sinitek.mind.support.openapi.interceptor;

import com.sinitek.mind.support.openapi.context.OpenApiContext;
import com.sinitek.mind.support.openapi.dto.OpenApiAuthResultDTO;
import com.sinitek.mind.support.openapi.service.IOpenApiAuthService;
import com.sinitek.mind.support.openapi.util.ApiKeyUtil;
import com.sinitek.sirm.common.um.RequestUser;
import com.sinitek.sirm.common.web.RequestContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * OpenAPI认证拦截器
 * 负责从请求头提取API密钥并进行认证验证
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Component
@Slf4j
public class OpenApiAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private IOpenApiAuthService authService;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {

        try {
            String apiKey = ApiKeyUtil.extractApiKey(request);
            if (StringUtils.isNotBlank(apiKey)) {
                // 认证API密钥
                OpenApiAuthResultDTO authResult = authService.authenticateApiKey(apiKey);

                // 将认证信息存储到ThreadLocal上下文中
                OpenApiContext.setAuth(authResult);

                RequestUser requestUser = authService.generateUserInfo(apiKey);
                RequestContext.setCurrentUser(requestUser);
                return true;
            }

            // 没有API密钥，返回401
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"Missing ApiKey Authorization header\"}");
            return false;
        } catch (Exception e) {
            log.warn("API密钥认证失败: {}", e.getMessage());
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"" + e.getMessage() + "\"}");
            return false;
        }
    }


    @Override
    public void afterCompletion(HttpServletRequest request,
                                HttpServletResponse response,
                                Object handler,
                                Exception ex) throws Exception {
        // 清理ThreadLocal，避免内存泄漏
        OpenApiContext.clear();
        RequestContext.end();
    }
} 