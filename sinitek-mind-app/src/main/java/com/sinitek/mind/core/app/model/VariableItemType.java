package com.sinitek.mind.core.app.model;

import lombok.Data;

import java.util.List;

@Data
public class VariableItemType {

    private String id;
    private String key;
    private String icon;
    private String label;
    private String type;// VariableInputEnum
    private boolean required;
    private String description;
    private String valueType; // WorkflowIOValueTypeEnum
    private Object defaultValue;

    // input
    private Integer maxLen;
    // numberInput
    private Integer max;
    private Integer min;
    // select
    private List<ListInfo> enums;
}
