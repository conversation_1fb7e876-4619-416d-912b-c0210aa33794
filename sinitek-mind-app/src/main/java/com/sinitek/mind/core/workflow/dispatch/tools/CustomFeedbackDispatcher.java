package com.sinitek.mind.core.workflow.dispatch.tools;

import com.sinitek.mind.core.chat.service.IChatService;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
public class CustomFeedbackDispatcher implements NodeDispatcher {

    @Autowired
    private IChatService chatService;

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        String chatId = dispatchData.getChatId();
        RunningAppInfo runningAppInfo = dispatchData.getRunningAppInfo();
        String appId = runningAppInfo.getId();
        String dataId = dispatchData.getResponseChatItemId();
        Map<String, Object> params = dispatchData.getParams();
        String feedbackText = (String) params.get(NodeInputKeyEnum.TEXTAREA_INPUT.getValue());

        // 异步进行保存
        CompletableFuture.runAsync(() -> {
            // 等待一分钟，确保在聊天项完全生成后再执行 addCustomFeedbacks。
            try {
                Thread.sleep(60000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            chatService.addCustomFeedbacks(appId, chatId, dataId, List.of(feedbackText));
        });

        if (StringUtils.isNotBlank(chatId) || StringUtils.isNotBlank(dataId)) {
            WorkflowStreamResponse workflowStreamResponse = WorkflowStreamResponse.builder()
                    .event(SseResponseEventEnum.FAST_ANSWER.getValue())
                    .data(WorkflowUtil.textAdaptGptResponse(TextAdaptGptResponseParams.builder()
                            .text("\\n\\n**自定义反馈成功: (仅调试模式下展示该内容)**: " + feedbackText + "\n\n")
                            .build())).build();
            dispatchData.getWorkflowStreamResponse().accept(workflowStreamResponse);
        }

        return Map.of(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
                .textOutput(feedbackText)
                .build());
    }
}
