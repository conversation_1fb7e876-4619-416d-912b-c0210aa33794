package com.sinitek.mind.core.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.enumerate.PerResourceTypeEnum;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.model.SourceMemberDTO;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.repository.AppVersionRepository;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.service.IImageService;
import com.sinitek.mind.core.app.util.AppUtil;
import com.sinitek.mind.core.chat.repository.ChatInputGuideRepository;
import com.sinitek.mind.core.chat.repository.ChatItemRepository;
import com.sinitek.mind.core.chat.repository.ChatRepository;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.openapi.repository.OpenApiRepository;
import com.sinitek.mind.support.operationlog.enumerate.OperationLogEventEnum;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.outlink.repository.OutLinkRepository;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppServiceImpl implements IAppService {

    private final MongoTemplate mongoTemplate;

    private final AppRepository appRepository;

    private final AppVersionRepository appVersionRepository;

    private final IOperationLogService operationLogService;

    private final ChatItemRepository chatItemRepository;

    private final ChatRepository chatRepository;

    private final OutLinkRepository outLinkRepository;

    private final OpenApiRepository openApiRepository;

    private final ChatInputGuideRepository chatInputGuideRepository;

    private final IImageService imageService;

    private final MongoTransactionManager mongoTransactionManager;

    private final ICollaboratorService collaboratorService;

    private final IAuthService authService;

    private final IPermissionService permissionService;

    private final IAccountService accountService;

    private final IAuthAppService authAppService;

    private final ITeamMemberService teamMemberService;

    private static final List<AppTypeEnum> APP_FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER);

    private static final List<String> FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER.getValue(), AppTypeEnum.HTTP_PLUGIN.getValue());

    @Override
    public List<AppListItemDTO> getAppList(ListAppDTO request) {
        // TODO 权限校验，需要重新修改
//        AuthModeType modeType = new  AuthModeType();
//        modeType.setAuthToken(true);
//        modeType.setAuthApiKey(true);
//        modeType.setPer(PermissionConstant.PERMISSION_LIST.get(PermissionKeyEnum.READ.getValue()).getValue());
//        AuthResult<?> authResult = permissionService.authUserPer(request, modeType);
        if (request.getParentId() != null) {
            authAppService.authApp(request.getParentId(), PermissionConstant.READ_PER);
        }

        // TODO 根据teamId获取该team所有app权限记录
//        TeamPermissionResultDTO perList = permissionService.getTeamPermissionResultDTO(authResult.getTeamId());

        Query query = buildAppQuery(request);

        // TODO 需要修改，此处只需要获取所有app，后续在此处进行处理即可
        return executeAppQuery(query, request);
    }

    @Override
    public String createApp(CreateAppDTO body) {
        // 校验参数
        String parentId = body.getParentId();
        String name = body.getName();
        String type = body.getType();
        List<StoreNodeItemType> modules = body.getModules();

        if (!StringUtils.hasText(name) || !StringUtils.hasText(type) || CollUtil.isEmpty(modules)) {
            throw new BussinessException("缺失必要参数");
        }

        AuthDTO authDTO;
        if (StringUtils.hasText(parentId)) {
            authDTO = authAppService.authApp(parentId, PermissionConstant.WRITE_PER);
        } else {
            authDTO = authService.authUserPer(PermissionConstant.APP_CREATE_PER);
        }

        String teamId = authDTO.getTeamId();
        String tmbId = authDTO.getTmbId();
        String userId = authDTO.getUserId();

        // app创建上限校验 TODO 需要确认是否需要该校验

        TeamMemberDTO teamMemberDTO = teamMemberService.getById(tmbId);

        body.setTeamId(teamId);
        body.setTmbId(tmbId);
        body.setUserAvatar(teamMemberDTO.getAvatar());
        body.setUsername(teamMemberDTO.getMemberName());


        // 创建应用
        String appId = onCreateApp(body);

        return appId;
    }

    @Override
    public AppDetailType getAppDetail(String appId) {
        if (appId == null || appId.isEmpty()) {
            throw new BussinessException(true, "appId不存在");
        }

        // 权限校验
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);

        App app = authAppDTO.getApp();
        AppUtil.rewriteAppWorkflowToDetail(app.getModules(), authAppDTO.getTeamId(), authAppDTO.getIsRoot(), app.getTmbId());

        AppDetailType appDetailType = new AppDetailType();
        BeanUtils.copyProperties(app, appDetailType);

        if (!authAppDTO.getPermission().getHasWritePer()) {
            appDetailType.setModules(List.of());
            appDetailType.setEdges(List.of());
        }

        // 设置_id
        appDetailType.set_id(appDetailType.getId());
        return appDetailType;
    }

    @Override
    public List<App> findAppAndAllChildren(String teamId, String appId) {
        App app = appRepository.findById(appId).orElse(null);
        if (app == null) {
            throw new BussinessException(true, "app not found");
        }
        List<App> childApps = findChildrenRecursively(teamId, appId);

        List<App> allApps = new ArrayList<>();
        allApps.add(app);
        allApps.addAll(childApps);
        return allApps;
    }

    @Override
    public List<AppBasicInfoDTO> getAppBasicInfoByIds(String teamId, List<String> ids) {
        List<App> apps = appRepository.findByTeamIdAndIdIn(teamId, ids);

        return apps.stream()
                .map(app -> new AppBasicInfoDTO(app.getId(), app.getName(), app.getAvatar()))
                .collect(Collectors.toList());
    }

    @Override
    public List<App> findByTeamId(String teamId) {
        return appRepository.findByTeamId(teamId);
    }

    @Override
    public List<App> findByTeamIdAndType(String teamId, String type) {
        return appRepository.findByTeamIdAndType(teamId, type);
    }

    @Override
    public List<App> findByTeamIdOrderByUpdateTimeDesc(String teamId) {
        return appRepository.findByTeamIdOrderByUpdateTimeDesc(teamId);
    }

    @Override
    public void deleteApp(String appId) {

        // TODO 需要确认需要什么样的权限
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);
        String teamId = authAppDTO.getTeamId();
        String tmbId = authAppDTO.getTmbId();
        String userId = authAppDTO.getUserId();
        App app1 = appRepository.findById(appId).orElse(null);
        List<App> children = appRepository.findChildrenByTeamIdAndParentId(teamId, appId);
        List<App> allApps = children;
        for (App app : children) {
            List<App> recursively = findChildrenRecursively(app.getTeamId(), app.getId());
            allApps.addAll(recursively);
        }
        allApps.add(app1);

        for (App app : allApps) {
            String id = app.getId();
            // 删除聊天相关
            // TODO deleteChatFiles
            chatItemRepository.deleteByAppId(id);

            chatRepository.deleteByAppId(id);

            // 删除分享链接
            outLinkRepository.deleteByAppId(id);

            // 删除 OpenAPI
            openApiRepository.deleteByAppId(id);

            // 删除版本
            appVersionRepository.deleteByAppId(id);

            // 删除输入引导
            chatInputGuideRepository.deleteByAppId(id);

            // 删除权限
            permissionService.deletePermission(id, ResourceTypeEnum.APP);

            // 删除 app
            appRepository.deleteById(id);

            // 删除头像图片
            imageService.removeImageByPath(app.getAvatar());
        }

        // 异步记录操作日志
        new Thread(() -> {
            Map<String, String> map = new HashMap<>();
            map.put("appName", app1 == null ? "" : app1.getName());
            map.put("appType", app1 == null ? "" : app1.getType());
            operationLogService.addOperationLog(OperationLogEventEnum.DELETE_APP, map);
        });
    }

    @Override
    public String copyApp(String appId) {
        String tmbId = "";
        App app = appRepository.findById(appId).orElse(null);
        if (app != null) {
            CreateAppDTO copyApp = new CreateAppDTO();
            copyApp.setParentId(app.getParentId());
            copyApp.setName(app.getName() + " Copy");
            copyApp.setIntro(app.getIntro());
            copyApp.setType(app.getType());
            copyApp.setModules(app.getModules());
            copyApp.setEdges(app.getEdges());
            copyApp.setChatConfig(app.getChatConfig());
            copyApp.setTeamId(app.getTeamId());
            copyApp.setTmbId(tmbId);
            copyApp.setPluginData(app.getPluginData());
            String newAppId = this.onCreateApp(copyApp);

            // 记录操作日志
            Map<String, String> map = new HashMap<>();
            map.put("appName", copyApp.getName());
            map.put("appType", copyApp.getType());
            operationLogService.addOperationLog(OperationLogEventEnum.CREATE_APP_COPY, map);
            return newAppId;
        }
        return "";
    }

    @Override
    public App updateApp(AppUpdateDTO params) {
        AtomicReference<App> result = new AtomicReference<>(new App());
        String appId = params.getAppId();
        if (appId == null || appId.isEmpty()) {
            throw new BussinessException(true, "缺少参数");
        }
        boolean isMove = params.getParentId() != null;
        // 2. 查询 app 及权限
        Optional<App> appOpt = appRepository.findById(appId);
        if (appOpt.isEmpty()) {
            throw new IllegalArgumentException("App not exist");
        }
        App app = appOpt.get();

        // 3 权限校验
        String targetName;
        String teamId = "";
        String tmbId = "";
        if (isMove) {
            Optional<App> targetAppOpt = appRepository.findById(params.getParentId());
            if (targetAppOpt.isEmpty()) {
                targetName = "";
                throw new BussinessException(true, "目标文件夹不存在");
            } else {
                targetName = "root";
            }
            if (app.getParentId() != null && !app.getParentId().isEmpty()) {
                // 原文件夹权限校验（略）
            }
            if (params.getParentId() == null || app.getParentId() == null || app.getParentId().isEmpty()) {
                // 根目录权限校验（略）
            }
        } else {
            targetName = "";
            // 非移动，校验写权限（略）
        }

        // 4. 事务处理
        TransactionTemplate template = new TransactionTemplate(mongoTransactionManager);
        template.execute(status -> {
            if (isMove) {
                // 如果是文件夹，继承权限
                if (AppTypeEnum.FOLDER.getValue().equals(app.getType())) {
                    // 获取父文件夹协作者和分组
                    List<String> parentClbsAndGroups = collaboratorService.getResourceClbsAndGroups(
                            app.getTeamId(), params.getParentId(), PerResourceTypeEnum.APP.getValue()
                    );
                    // 同步自身协作者
                    collaboratorService.syncCollaborators(app.getId(), PerResourceTypeEnum.APP.getValue(), parentClbsAndGroups, app.getTeamId());
                    // 同步子节点协作者
                    collaboratorService.syncChildrenPermission(app, PerResourceTypeEnum.APP.getValue(), parentClbsAndGroups);
                } else {
                    logAppMove(tmbId, teamId, app, targetName);
                    // 非文件夹，删除所有协作者
                    permissionService.deletePermission(app.getId(), ResourceTypeEnum.APP);
                }
                // 更新
                result.set(doUpdate(app, params, appId, true));
            } else {
                logAppUpdate(tmbId, teamId, app, params.getName(), params.getIntro());
                result.set(doUpdate(app, params, appId, false));
            }
            return result;
        });
        return result.get();
    }

    @Override
    public String transitionWorkFlow(TransitionWorkflowDTO body) {
        String appId = body.getAppId();
        boolean createNew = body.isCreateNew();
        // TODO 需要确认什么样的权限
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        if (createNew) {
            CreateAppDTO appNew = new CreateAppDTO();
            appNew.setParentId(app.getParentId());
            appNew.setName(app.getName() + " Copy");
            appNew.setAvatar(app.getAvatar());
            appNew.setType(app.getType());
            appNew.setModules(app.getModules());
            appNew.setEdges(app.getEdges());
            appNew.setChatConfig(app.getChatConfig());
            appNew.setTeamId(app.getTeamId());
            appNew.setTmbId(app.getTmbId());
            return createApp(appNew);
        }
        updateAppType(appId, AppTypeEnum.WORKFLOW.getValue());
        return "";
    }

    @Override
    public String resumeInheritPermission(String appId) {
        // TODO 需要确认什么样的权限
        AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);
        App app = authAppDTO.getApp();
        assert app != null;
        if (app.getParentId() != null) {
            // TODO
        } else {
            Query query = new Query(Criteria.where("_id").is(appId));
            Update update = new Update().set("inheritPermission", true);
            mongoTemplate.updateFirst(query, update, App.class);
        }
        return appId;
    }

    @Override
    public List<BasicInfoResDTO> getBasicInfo(String teamId, List<String> ids) {
        List<App> apps = appRepository.findByTeamIdAndIdIn(teamId, ids);
        List<BasicInfoResDTO> res = new ArrayList<>();
        for (App app : apps) {
            BasicInfoResDTO basicInfoResDTO = new BasicInfoResDTO();
            basicInfoResDTO.setId(app.getId());
            basicInfoResDTO.setName(app.getName());
            basicInfoResDTO.setAvatar(app.getAvatar());
            res.add(basicInfoResDTO);
        }
        return res;
    }

    private void updateAppType(String appId, String type) {
        Query query = new Query(Criteria.where("_id").is(appId));
        Update update = new Update().set("type", type);
        mongoTemplate.updateFirst(query, update, App.class);
    }

    private App doUpdate(App app, AppUpdateDTO req, String appId, boolean isMove) {
        // TODO 格式化 nodes 数据（略，假设 beforeUpdateAppFormat 已实现）
        // beforeUpdateAppFormat(req.getNodes());

        // TODO 刷新头像（略，假设 refreshSourceAvatar 已实现）
        // refreshSourceAvatar(req.getAvatar(), app.getAvatar());

        // 如果是工具集类型且有新头像，批量更新子应用头像
        if (AppTypeEnum.TOOL_SET.getValue().equals(app.getType()) && req.getAvatar() != null && !req.getAvatar().isEmpty()) {
            appRepository.updateAvatarByParentIdAndTeamId(req.getAvatar(), appId, app.getTeamId());
        }

        // 构建更新内容
        if (req.getParentId() != null) app.setParentId(req.getParentId());
        if (req.getName() != null) app.setName(req.getName());
        if (req.getType() != null) app.setType(req.getType());
        if (req.getAvatar() != null) app.setAvatar(req.getAvatar());
        if (req.getIntro() != null) app.setIntro(req.getIntro());
        if (req.getTeamTags() != null) app.setTeamTags(req.getTeamTags());
        if (req.getNodes() != null) app.setModules(req.getNodes());
        if (req.getEdges() != null) app.setEdges(req.getEdges());
        if (req.getChatConfig() != null) app.setChatConfig(req.getChatConfig());
        if (isMove) app.setInheritPermission(true);

        appRepository.save(app);
        return app;
    }

    private void logAppMove(String tmbId, String teamId, App app, String targetName) {
        Map<String,String> map = new HashMap<>();
        map.put("appName", app.getName());
        map.put("targetFolderName", targetName);
        map.put("appType", app.getType());
        operationLogService.addOperationLog(
                OperationLogEventEnum.MOVE_APP,
                map
        );
    }

    private void logAppUpdate(String tmbId, String teamId, App app, String name, String intro) {
        StringBuilder names = new StringBuilder();
        StringBuilder values = new StringBuilder();

        if (name != null) {
            names.append(AppUtil.i18n("common:name")).append(",");
            values.append(name).append(",");
        }
        if (intro != null) {
            names.append(AppUtil.i18n("common:Intro")).append(",");
            values.append(intro).append(",");
        }
        Map<String,String> map = new HashMap<>();
        map.put("appName", app.getName());
        map.put("newItemNames", String.valueOf(names));
        map.put("newItemValues", String.valueOf(values));
        map.put("appType", app.getType());
        operationLogService.addOperationLog(
                OperationLogEventEnum.UPDATE_APP_INFO,
                map
        );
    }

    /**
     * 递归查找所有子应用
     */
    private List<App> findChildrenRecursively(String teamId, String parentId) {
        List<App> children = appRepository.findByTeamIdAndParentId(teamId, parentId);
        List<App> allChildren = new ArrayList<>(children);

        // 递归查找每个子应用的子应用
        for (App child : children) {
            List<App> grandChildren = findChildrenRecursively(teamId, child.getId());
            allChildren.addAll(grandChildren);
        }

        return allChildren;
    }

    private String onCreateApp(CreateAppDTO body) {
        App app = new App();
        app.setAvatar(body.getAvatar());
        app.setName(body.getName());
        app.setIntro(body.getIntro());
        app.setTeamId(body.getTeamId());
        app.setTmbId(body.getTmbId());
        app.setModules(body.getModules());
        app.setEdges(body.getEdges());
        app.setChatConfig(body.getChatConfig());
        app.setType(body.getType());
        app.setVersion("v2");
        app.setPluginData(body.getPluginData());
        app.setUpdateTime(new Date());

        // 如果有父级ID，设置父级关系
        if (body.getParentId() != null) {
            app.setParentId(body.getParentId());
        }
        // 保存应用
        App savedApp = appRepository.save(app);
        String appId = savedApp.getId();

        if (!APP_FOLDER_TYPE_LIST.contains(AppTypeEnum.fromValue(savedApp.getType()))) {
            AppVersion appVersion = new AppVersion();
            appVersion.setTmbId(body.getTmbId());
            appVersion.setAppId(appId);
            appVersion.setNodes(body.getModules());
            appVersion.setEdges(body.getEdges());
            appVersion.setChatConfig(body.getChatConfig());
            appVersion.setVersionName(body.getName());
            appVersion.setUsername(body.getUsername());
            appVersion.setAvatar(body.getUserAvatar());
            appVersion.setIsPublish(true);
            appVersionRepository.save(appVersion);
        }

        // 记录操作日志
        Map<String, String> map = new HashMap<>();
        map.put("appName", body.getName());
        map.put("appType", body.getType());
        operationLogService.addOperationLog(OperationLogEventEnum.CREATE_APP, map);

        return appId;
    }

    private Query buildAppQuery(ListAppDTO request) {
        Query query = new Query();

        // 处理最近聊天应用查询
        if (Boolean.TRUE.equals(request.getGetRecentlyChat())) {
            query.addCriteria(Criteria.where("type").in(Arrays.asList(
                    AppTypeEnum.WORKFLOW.getValue(),
                    AppTypeEnum.SIMPLE.getValue(),
                    AppTypeEnum.PLUGIN.getValue()
            )));
            return query;
        }

        // 处理搜索条件
        if (StringUtils.hasText(request.getSearchKey())) {
            String searchKey = escapeRegexChars(request.getSearchKey());
            query.addCriteria(new Criteria().orOperator(
                    Criteria.where("name").regex(searchKey, "i"),
                    Criteria.where("intro").regex(searchKey, "i")
            ));
        }

        // 处理类型过滤
        if (request.getType() != null) {
            query.addCriteria(Criteria.where("type").in(request.getType()));
        }

        // 处理父级ID过滤
        if (request.getParentId() != null) {
            query.addCriteria(Criteria.where("parentId").is(request.getParentId()));
        }

        return query;
    }

    private List<AppListItemDTO> executeAppQuery(Query query, ListAppDTO request) {
        // 设置排序
        query.with(Sort.by(Sort.Direction.DESC, "updateTime"));

        // 设置限制
        if (Boolean.TRUE.equals(request.getGetRecentlyChat())) {
            query.limit(15);
        } else if (StringUtils.hasText(request.getSearchKey())) {
            query.limit(50);
        }

        String orgId = CurrentUserFactory.getOrgId();

        List<App> apps = mongoTemplate.find(query, App.class);
        List<AppListItemDTO> appListItemDTOS = new ArrayList<>();
        for (App app : apps) {
            AppListItemDTO appListItemDTO = new AppListItemDTO();
            appListItemDTO.setName(app.getName());
            appListItemDTO.setAvatar(app.getAvatar());
            appListItemDTO.setType(app.getType());
            appListItemDTO.set_id(app.getId());
            appListItemDTO.setTmbId(app.getTmbId());
            appListItemDTO.setIntro(app.getIntro());
            appListItemDTO.setUpdateTime(app.getUpdateTime());
            appListItemDTO.setPluginData(app.getPluginData());
            appListItemDTO.setInheritPermission(app.getInheritPermission());

            // TODO 需要进行优化，这里的权限校验应该支持批量校验
            try {
                AuthAppDTO authAppDTO = authAppService.authApp(app.getId(), PermissionConstant.READ_PER);
                appListItemDTO.setPermission(authAppDTO.getPermission());
            } catch (Exception e) {
                // 报错说明无权限，则不返回该条数据
                continue;
            }

            SourceMemberDTO sourceMember = accountService.getSourceMemberByOrgId(app.getTmbId());
            appListItemDTO.setSourceMember(sourceMember);

            appListItemDTOS.add(appListItemDTO);
        }

        return appListItemDTOS;
    }

    private String escapeRegexChars(String input) {
        if (input == null) {
            return null;
        }
        return input.replaceAll("[.*+?^${}()|\\[\\]\\\\]", "\\\\$0");
    }

    private int concatPermissions(List<Integer> permissions) {
        return permissions.stream()
                .mapToInt(Integer::intValue)
                .reduce(0, (a, b) -> a | b);
    }
}
