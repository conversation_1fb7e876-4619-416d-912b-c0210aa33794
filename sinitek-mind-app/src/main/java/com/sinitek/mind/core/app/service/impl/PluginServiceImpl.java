package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.AppVersionDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.enumerate.FlowNodeTemplateTypeEnum;
import com.sinitek.mind.core.app.enumerate.PluginSourceEnum;
import com.sinitek.mind.core.app.model.*;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.repository.AppVersionRepository;
import com.sinitek.mind.core.app.service.IAppVersionService;
import com.sinitek.mind.core.app.service.IFileTemplateService;
import com.sinitek.mind.core.app.service.IPluginService;
import com.sinitek.mind.core.app.util.AppUtil;
import com.sinitek.mind.core.app.util.PluginUtils;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PluginServiceImpl implements IPluginService {

    private final AppRepository appRepository;

    private final AppVersionRepository appVersionRepository;

    private final IAppVersionService appVersionService;

    private final IFileTemplateService fileTemplateService;

    /**
     * 获取子应用预览节点
     * 对应原始的getChildAppPreviewNode函数
     *
     * @param appId 应用ID
     * @param versionId 版本ID（可选）
     * @return FlowNodeTemplateType 流程节点模板
     */
    @Override
    public FlowNodeTemplateType getChildAppPreviewNode(String appId, String versionId) {
        // 校验参数
        if (!StringUtils.hasText(appId)) {
            throw new BussinessException("appId不能为空");
        }

        SplitCombineToolIdRes splitCombineToolIdRes = PluginUtils.splitCombineToolId(appId);
        String pluginId = splitCombineToolIdRes.getPluginId();
        String source = splitCombineToolIdRes.getSource();

        ChildAppType app = null;
        if (PluginSourceEnum.PERSONAL.getValue().equals(source)) {
            // 个人插件，查询数据库
            // 查找app
            App item = appRepository.findById(appId).orElseThrow(() -> new BussinessException("app不存在"));

            // 获取应用版本
            AppVersion appVersion = null;
            boolean isLatest = false;
            if (StringUtils.hasText(versionId)) {
                appVersion = appVersionRepository.findByAppIdAndId(new ObjectId(appId), new ObjectId(versionId))
                        .orElseThrow(() -> new BussinessException("应用版本不存在"));
                isLatest = appVersionService.checkIsLatestVersion(appId, appVersion.getId());
            } else {
                // 获取最新版本
                appVersion = appVersionRepository.findLatestByAppId(new ObjectId(appId))
                        .orElseThrow(() -> new BussinessException("应用版本不存在"));
                isLatest = true;
            }

            app = ChildAppType.builder()
                    .id(item.getId())
                    .teamId(item.getTeamId())
                    .name(item.getName())
                    .avatar(item.getAvatar())
                    .intro(item.getIntro())
                    .showStatus(true)
                    .workflow(new WorkflowTemplateBasicType(appVersion.getNodes(), appVersion.getEdges(), appVersion.getChatConfig()))
                    .templateType(FlowNodeTemplateTypeEnum.TEAM_APP.getValue())
                    .version(StringUtils.hasText(versionId) ? appVersion.getId() : "")
                    .versionLabel(appVersion.getVersionName())
                    .isLatestVersion(isLatest)
                    .originCost(0)
                    .currentCost(0)
                    .hasTokenFee(false)
                    .pluginOrder(0)
                    .build();
        } else {
            app = getSystemPluginTemplateById(pluginId, versionId);
        }

        List<StoreNodeItemType> nodes = app.getWorkflow().getNodes();
        boolean isPlugin = nodes.stream()
                .anyMatch(node -> FlowNodeTypeEnum.PLUGIN_INPUT.getValue().equals(node.getFlowNodeType()));

        boolean isTool = nodes.stream()
                .anyMatch(node -> FlowNodeTypeEnum.TOOL.getValue().equals(node.getFlowNodeType()) &&
                        nodes.size() == 1);

        boolean isToolSet = nodes.stream()
                .anyMatch(node -> FlowNodeTypeEnum.TOOL_SET.getValue().equals(node.getFlowNodeType()) &&
                        nodes.size() == 1);

        AppChatConfigType chatConfig = app.getWorkflow().getChatConfig();
        Supplier<Tuple2<String, NodeIOConfig>> supplier = () -> {
            if (isToolSet) {
                return Tuples.of(FlowNodeTypeEnum.TOOL_SET.getValue(), PluginUtils.toolSetData2FlowNodeIO(nodes));
            } else if (isTool) {
                return Tuples.of(FlowNodeTypeEnum.TOOL.getValue(), PluginUtils.toolData2FlowNodeIO(nodes));
            } else if (isPlugin) {
                return Tuples.of(FlowNodeTypeEnum.PLUGIN_MODULE.getValue(), PluginUtils.pluginData2FlowNodeIO(nodes));
            } else {
                return Tuples.of(FlowNodeTypeEnum.APP_MODULE.getValue(), PluginUtils.appData2FlowNodeIO(chatConfig));
            }
        };
        Tuple2<String, NodeIOConfig> tuple2 = supplier.get();
        String flowNodeType = tuple2.getT1();
        NodeIOConfig nodeIOConfig = tuple2.getT2();


        // 构建FlowNodeTemplate
        FlowNodeTemplateType template = new FlowNodeTemplateType();
        // 基本信息
        template.setId(WorkflowUtil.getNanoid(16));
        template.setPluginId(app.getId());
        template.setTemplateType(app.getTemplateType());
        template.setFlowNodeType(flowNodeType);
        template.setAvatar(app.getAvatar());
        template.setName(app.getName());
        template.setIntro(app.getIntro());
        template.setCourseUrl(app.getCourseUrl());
        template.setUserGuide(app.getUserGuide());
        template.setShowStatus(app.getShowStatus());
        template.setIsTool(true);

        // 版本信息
        template.setVersion(app.getVersion());
        template.setVersionLabel(app.getVersionLabel());
        template.setIsLatestVersion(app.getIsLatestVersion());

        // 输入输出
        template.setInputs(nodeIOConfig.getInputs());
        template.setOutputs(nodeIOConfig.getOutputs());

        // 处理句柄
        if (isToolSet) {
            template.setSourceHandle(PluginUtils.getHandleConfig(false, false, false, false));
            template.setTargetHandle(PluginUtils.getHandleConfig(false, false, false, false));
        } else {
            template.setSourceHandle(PluginUtils.getHandleConfig(true, true, true, true));
            template.setTargetHandle(PluginUtils.getHandleConfig(true, true, true, true));
        }
        return template;
    }

    @Override
    public List<NodeTemplateListItem> getSystemPluginTemplates(String searchKey, String parentId) {
        List<SystemPluginTemplateItemType> pluginTemplate = fileTemplateService.getPluginTemplate();

        return pluginTemplate.stream().filter(SystemPluginTemplateItemType::getIsActive)
                .map(plugin -> {
                    NodeTemplateListItem  node = new NodeTemplateListItem();
                    node.id = plugin.id;
                    node.isFolder = plugin.isFolder;
                    node.parentId = plugin.parentId;
                    node.templateType = (plugin.templateType != null)
                            ? plugin.templateType
                            : FlowNodeTemplateTypeEnum.OTHER.getValue();
                    node.flowNodeType = FlowNodeTypeEnum.PLUGIN_MODULE.getValue();
                    node.avatar = plugin.avatar;
                    node.name = plugin.name;
                    node.intro = plugin.intro;
                    node.isTool = plugin.isTool;
                    node.currentCost = plugin.currentCost;
                    node.hasTokenFee = plugin.hasTokenFee;
                    node.author = plugin.author;
                    node.instructions = plugin.userGuide; // userGuide -> instructions
                    node.courseUrl = plugin.courseUrl;
                    return node;
                })
                // 根据搜索条件过滤
                .filter(item -> {
                    if (searchKey != null && !searchKey.isEmpty()) {
                        if (item.isFolder) return false;

                        // 创建不区分大小写的正则表达式
                        String pattern = Pattern.quote(searchKey);
                        Pattern regx = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);

                        // 检查名称或简介匹配
                        boolean nameMatch = item.name != null &&
                                regx.matcher(item.name).find();
                        boolean introMatch = item.intro != null &&
                                regx.matcher(item.intro).find();
                        return nameMatch || introMatch;
                    } else {
                        // 无搜索关键词时匹配父ID
                        return Objects.equals(item.parentId, parentId);
                    }
                })
                .toList();
    }

    @Override
    public PageResult<VersionListItemType> getVersionList(String toolId, int offset, int pageSize, String userId) {
        if (!StringUtils.hasText(toolId)) {
            throw new BussinessException("缺少参数：toolId");
        }

        SplitCombineToolIdRes splitCombineToolIdRes = PluginUtils.splitCombineToolId(toolId);
        String source =  splitCombineToolIdRes.getSource();
        String appId = splitCombineToolIdRes.getPluginId();

        if (PluginSourceEnum.PERSONAL.getValue().equals(source)) {
            // 个人插件，查询数据库
            return getPersonalPluginVersions(appId, offset, pageSize, userId);
        } else if (PluginSourceEnum.COMMUNITY.getValue().equals(source)) {
            // 社区插件 TODO 从系统插件获取
        } else if (PluginSourceEnum.COMMERCIAL.getValue().equals(source)) {

        } else {
            throw new BussinessException("未知的插件来源");
        }
        return null;
    }

    /**
     * 获取插件路径
     * @param pluginId 插件ID
     * @param type 类型：current 或 parent
     * @return 路径列表
     */
    @Override
    public List<ParentTreePathItemType> getPluginPath(String pluginId, String type) {
        log.info("获取插件路径，插件ID: {}, 类型: {}", pluginId, type);

        if (pluginId == null || pluginId.trim().isEmpty()) {
            log.debug("插件ID为空，返回空路径列表");
            return new ArrayList<>();
        }

        // 获取系统插件列表
        List<NodeTemplateListItem> plugins = this.getSystemPluginTemplates(null, null);

        // 查找指定的插件
        NodeTemplateListItem plugin = plugins.stream()
                .filter(item -> pluginId.equals(item.getId()))
                .findFirst()
                .orElse(null);

        if (plugin == null) {
            log.debug("未找到插件ID为 {} 的插件，返回空路径列表", pluginId);
            return new ArrayList<>();
        }

        // 构建路径项
        List<ParentTreePathItemType> pathList = new ArrayList<>();

        String parentId;
        if ("current".equals(type)) {
            parentId = plugin.getId();
        } else {
            parentId = plugin.getParentId();
        }

        ParentTreePathItemType pathItem = new ParentTreePathItemType(parentId, plugin.getName());
        pathList.add(pathItem);

        log.debug("返回路径列表: {}", pathList);
        return pathList;
    }

    @Override
    public PluginRuntimeType getChildAppRuntimeById(String id, String versionId) {
        ChildAppType app = getApp(id, versionId);
        PluginRuntimeType type = new PluginRuntimeType();
        assert app != null;
        type.setId(app.getId());
        type.setTeamId(app.getTeamId());
        type.setTmbId(app.getTmbId());
        type.setName(app.getName());
        type.setAvatar(app.getAvatar());
        type.setShowStatus(app.showStatus);
        type.setCurrentCost(Double.valueOf(app.getCurrentCost()));
        type.setNodes(app.getWorkflow().getNodes());
        type.setEdges(app.getWorkflow().getEdges());
        type.setHasTokenFee(app.hasTokenFee);

        return type;
    }

    @Override
    public ChildAppType getSystemPluginTemplateById(String id, String versionId) {
        // TODO getSystemPluginTemplates()
        return null;
    }

    private ChildAppType getApp(String id, String versionId) {
        SplitCombineToolIdRes splitCombineToolIdRes = AppUtil.splitCombineToolId(id);
        String source = splitCombineToolIdRes.getSource();
        String pluginId = splitCombineToolIdRes.getPluginId();

        if (source.equals(PluginSourceEnum.PERSONAL.getValue())) {
            App item = appRepository.findById(id).orElse(null);

            if (item == null) {
                return null;
            }

            AppVersionDTO version = appVersionService.getAppVersionById(id, versionId, item);

            WorkflowTemplateBasicType basicType = new WorkflowTemplateBasicType();
            basicType.setNodes(version.getNodes());
            basicType.setEdges(version.getEdges());
            basicType.setChatConfig(version.getChatConfig());

            ChildAppType plugin = new ChildAppType();
            plugin.setId(item.getId());
            plugin.setTmbId(item.getTmbId());
            plugin.setTeamId(item.getTeamId());
            plugin.setName(item.getName());
            plugin.setIntro(item.getIntro());
            plugin.setAvatar(item.getAvatar());
            plugin.setShowStatus(true);
            plugin.setWorkflow(basicType);
            plugin.setTemplateType(FlowNodeTemplateTypeEnum.TEAM_APP.getValue());

            plugin.setOriginCost(0);
            plugin.setCurrentCost(0);
            plugin.setHasTokenFee(false);
            plugin.setPluginOrder(0);
            return plugin;
        } else {
            return getSystemPluginTemplateById(pluginId, versionId);
        }
    }

    /**
     * 获取个人插件版本列表
     *
     * @param appId 应用ID
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @param userId 用户ID
     * @return 版本列表响应
     */
    private PageResult<VersionListItemType> getPersonalPluginVersions(String appId, int offset, int pageSize, String userId) {
        PageResult<VersionListItemType> result = new PageResult<>();
        // 创建分页请求
        Pageable pageable = PageRequest.of(offset / pageSize, pageSize, Sort.by(Sort.Direction.DESC, "createTime"));

        // 查询已发布的版本
        Page<AppVersion> versionsPage = appVersionRepository.findByAppIdAndIsPublishTrue(new ObjectId(appId), pageable);

        // 转换为响应对象
        List<VersionListItemType> versionItems = versionsPage.getContent().stream()
                .map(this::convertToVersionListItem)
                .collect(Collectors.toList());
        result.setTotal((int) versionsPage.getTotalElements());
        result.setList(versionItems);
        return result;
    }

    /**
     * 将AppVersion实体转换为VersionListItem
     *
     * @param appVersion 应用版本实体
     * @return 版本列表项
     */
    private VersionListItemType convertToVersionListItem(AppVersion appVersion) {
        VersionListItemType item = new VersionListItemType();
        item.setId(appVersion.getId());
        item.setAppId(appVersion.getAppId());
        item.setVersionName(appVersion.getVersionName());
        item.setTime(appVersion.getCreateTime());
        item.setIsPublish(appVersion.getIsPublish()); // 已发布的版本
        item.setTmbId(appVersion.getId()); // 使用版本ID作为模板ID
        return item;
    }

    /**
     * 生成节点ID
     */
    private String generateNodeId(String appId, String versionId) {
        if (StringUtils.hasText(versionId)) {
            return "runApp-" + appId + "-" + versionId;
        }
        return "runApp-" + appId;
    }

    /**
     * 生成插件ID
     */
    private String generatePluginId(App app) {
        // 根据应用类型生成插件ID
        if ("plugin".equals(app.getType())) {
            return PluginUtils.combineTool(
                    PluginSourceEnum.PERSONAL,
                    app.getId()
            );
        }
        return app.getId();
    }

    /**
     * 生成输入配置
     */
    private List<FlowNodeInputItemType> generateInputs(AppVersion appVersion) {
        List<FlowNodeInputItemType> inputs = new ArrayList<>();

        // 系统输入
        FlowNodeInputItemType systemInput = new FlowNodeInputItemType();
        systemInput.setKey("system");
        systemInput.setRenderTypeList(List.of("textarea"));
        systemInput.setValueType("string");
        systemInput.setLabel("系统提示词");
        systemInput.setDescription("系统级别的提示词配置");
        systemInput.setRequired(false);
        systemInput.setPlaceholder("请输入系统提示词");
        inputs.add(systemInput);

        // 用户问题输入
        FlowNodeInputItemType userInput = new FlowNodeInputItemType();
        userInput.setKey("userChatInput");
        userInput.setRenderTypeList(List.of("reference"));
        userInput.setValueType("string");
        userInput.setLabel("用户问题");
        userInput.setDescription("用户输入的问题内容");
        userInput.setRequired(true);
        userInput.setToolDescription("用户当前的问题");
        inputs.add(userInput);

        // 历史记录输入
        FlowNodeInputItemType historyInput = new FlowNodeInputItemType();
        historyInput.setKey("history");
        historyInput.setRenderTypeList(List.of("reference"));
        historyInput.setValueType("chatHistory");
        historyInput.setLabel("聊天记录");
        historyInput.setDescription("历史对话记录");
        historyInput.setRequired(false);
        inputs.add(historyInput);

        return inputs;
    }

    /**
     * 生成输出配置
     */
    private List<FlowNodeOutputItemType> generateOutputs(AppVersion appVersion) {
        List<FlowNodeOutputItemType> outputs = new ArrayList<>();

        // 文本输出
        FlowNodeOutputItemType textOutput = new FlowNodeOutputItemType();
        textOutput.setId("text");
        textOutput.setKey("text");
        textOutput.setLabel("AI回复");
        textOutput.setDescription("AI模型的回复内容");
        textOutput.setValueType("string");
        textOutput.setType("static");
        outputs.add(textOutput);

        // 完成状态输出
        FlowNodeOutputItemType finishOutput = new FlowNodeOutputItemType();
        finishOutput.setId("finish");
        finishOutput.setKey("finish");
        finishOutput.setLabel("完成");
        finishOutput.setDescription("任务完成状态");
        finishOutput.setValueType("boolean");
        finishOutput.setType("static");
        outputs.add(finishOutput);

        return outputs;
    }

    /**
     * 生成源处理句柄
     */
    private HandleType generateSourceHandle() {
        HandleType handleType = new HandleType();
        handleType.setRight(true);
        return handleType;
    }

    /**
     * 生成目标处理句柄
     */
    private HandleType generateTargetHandle() {
        HandleType handleType = new HandleType();
        handleType.setLeft(true);
        return handleType;
    }
}
